# MenuEditor 删除状态持久化修复测试

## 修复内容

### 1. 问题描述
当用户删除菜品后，如果跳转到编辑菜品页面再返回，merge数据时被删除的菜品仍然会重新显示出来。

### 2. 修复方案
在数据merge过程中（页面重新激活时的数据刷新），应该过滤掉所有已记录的被删除菜品ID。

### 3. 具体修改

#### 3.1 fetchMenuData 函数中的招牌菜收集逻辑
- **位置**: 第394-421行
- **修改**: 在收集招牌菜时增加删除状态检查
- **代码**: 添加 `&& !deletedDishIds.value.has(dish.dishId)` 条件

#### 3.2 specialtyGroup 计算属性
- **位置**: 第164-188行  
- **修改**: 在过滤招牌菜时增加删除状态检查
- **代码**: 添加 `&& !deletedDishIds.value.has(dish.dishId)` 条件

#### 3.3 recalculateSpecialtyOrder 函数
- **位置**: 第253-301行
- **修改**: 在重新计算招牌菜排序时过滤已删除菜品
- **代码**: 
  - 先过滤出有效的招牌菜: `validSpecialtyDishes`
  - 只对有效菜品重新计算排序
  - 更新specialtyDishes数组，移除已删除的菜品

#### 3.4 onUpdateGroup 方法
- **位置**: 第661-691行
- **修改**: 在处理菜品列表更新时检查删除状态
- **代码**: 添加 `|| deletedDishIds.value.has(dish.dishId)` 条件

#### 3.5 onSortDishes 方法
- **位置**: 第923-958行
- **修改**: 在菜品排序时过滤已删除菜品
- **代码**: 在两个过滤逻辑中都添加 `&& !deletedDishIds.value.has(dish.dishId)` 条件

### 4. 现有的删除记录管理
- **store模块**: `menuManage.ts` 已经实现了完整的删除记录管理
- **删除记录**: 使用 `Set<string>` 存储已删除的菜品ID和分组ID
- **数据合并**: `mergeMenuData` 函数已经正确过滤删除的菜品和分组

### 5. 测试场景

#### 5.1 基本删除测试
1. 删除一个普通菜品
2. 跳转到编辑其他菜品页面
3. 返回菜单编辑页面
4. **预期**: 被删除的菜品不应该重新出现

#### 5.2 招牌菜删除测试
1. 删除一个招牌菜
2. 跳转到编辑其他菜品页面  
3. 返回菜单编辑页面
4. **预期**: 被删除的招牌菜不应该在招牌菜分组和普通分组中重新出现

#### 5.3 多菜品删除测试
1. 删除多个菜品（包括招牌菜和普通菜品）
2. 跳转到编辑其他菜品页面
3. 返回菜单编辑页面
4. **预期**: 所有被删除的菜品都不应该重新出现

#### 5.4 数据一致性测试
1. 删除招牌菜后检查specialtyGroup计算属性
2. 检查menuGroups数组中对应菜品的删除状态
3. **预期**: 两个数据结构保持一致，都不包含已删除菜品

### 6. 关键改进点

1. **全面的删除过滤**: 在所有涉及菜品数据处理的地方都增加了删除状态检查
2. **招牌菜数据一致性**: 确保招牌菜数据和普通分组数据的删除状态同步
3. **防御性编程**: 保持原有的防御性检查，增强代码健壮性
4. **性能优化**: 使用Set数据结构进行快速删除状态查询

### 7. 注意事项

1. **删除记录清理**: 现有逻辑已经在数据合并时清理不再需要的删除记录
2. **临时分组**: 临时分组的删除不会记录到删除记录中
3. **数据合并策略**: 保持原有的智能合并逻辑，只是增加删除过滤功能
