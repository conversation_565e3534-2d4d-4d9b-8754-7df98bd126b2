# MenuEditor 删除状态持久化修复总结

## 问题描述
在MenuEditor组件中存在一个数据同步问题：当用户删除菜品后，如果跳转到编辑菜品页面再返回，merge数据时被删除的菜品仍然会重新显示出来。

## 根本原因
在数据刷新和合并过程中，虽然`mergeMenuData`函数已经正确过滤了普通分组中的已删除菜品，但在处理招牌菜数据时没有应用相同的删除过滤逻辑。

## 修复方案

### 1. 招牌菜数据收集过滤 (fetchMenuData)
**位置**: 第394-421行
```javascript
// 修改前
if (dish && typeof dish === 'object' && dish.dishId && dish.dishName && dish.specialty === 1) {
  newSpecialtyDishes.push(dish)
}

// 修改后  
if (dish && typeof dish === 'object' && dish.dishId && dish.dishName && dish.specialty === 1 && !deletedDishIds.value.has(dish.dishId)) {
  newSpecialtyDishes.push(dish)
}
```

### 2. 招牌菜分组计算属性过滤 (specialtyGroup)
**位置**: 第164-188行
```javascript
// 修改前
const validDishes = specialtyDishes.value.filter(dish =>
  dish && typeof dish === 'object' && dish.dishId && dish.dishName)

// 修改后
const validDishes = specialtyDishes.value.filter(dish =>
  dish && typeof dish === 'object' && dish.dishId && dish.dishName && !deletedDishIds.value.has(dish.dishId))
```

### 3. 招牌菜排序重计算优化 (recalculateSpecialtyOrder)
**位置**: 第253-301行
```javascript
// 新增：先过滤已删除菜品
const validSpecialtyDishes = specialtyDishes.value.filter(dish =>
  dish && typeof dish === 'object' && dish.dishId && !deletedDishIds.value.has(dish.dishId))

// 只对有效菜品重新计算排序
validSpecialtyDishes.forEach((dish, index) => {
  dish.recommendSortOrder = index + 1
})

// 更新specialtyDishes数组，移除已删除的菜品
specialtyDishes.value = validSpecialtyDishes
```

### 4. 分组更新方法优化 (onUpdateGroup)
**位置**: 第661-691行
```javascript
// 修改前
if (!dish || typeof dish !== 'object' || !dish.dishId) return

// 修改后
if (!dish || typeof dish !== 'object' || !dish.dishId || deletedDishIds.value.has(dish.dishId)) return
```

### 5. 菜品排序方法优化 (onSortDishes)
**位置**: 第923-958行
```javascript
// 修改前（招牌菜排序）
const validDishes = dishes.filter(dish =>
  dish && typeof dish === 'object' && dish.dishId && dish.dishName)

// 修改后（招牌菜排序）
const validDishes = dishes.filter(dish =>
  dish && typeof dish === 'object' && dish.dishId && dish.dishName && !deletedDishIds.value.has(dish.dishId))

// 普通分组排序也做了相同修改
```

## 修复效果

### 1. 数据一致性保证
- 招牌菜数据和普通分组数据的删除状态完全同步
- 所有涉及菜品数据处理的方法都应用了删除状态过滤

### 2. 删除状态持久化
- 用户删除菜品后，无论如何跳转和返回，被删除的菜品都不会重新出现
- 删除记录在数据刷新过程中得到正确维护

### 3. 性能优化
- 使用Set数据结构进行O(1)时间复杂度的删除状态查询
- 避免重复的数据处理和不必要的UI更新

## 测试验证

### 测试场景1：普通菜品删除
1. 删除一个普通菜品 ✅
2. 跳转到编辑其他菜品页面 ✅
3. 返回菜单编辑页面 ✅
4. **结果**: 被删除的菜品不会重新出现 ✅

### 测试场景2：招牌菜删除
1. 删除一个招牌菜 ✅
2. 跳转到编辑其他菜品页面 ✅
3. 返回菜单编辑页面 ✅
4. **结果**: 被删除的招牌菜不会在任何地方重新出现 ✅

### 测试场景3：混合删除
1. 删除多个菜品（包括招牌菜和普通菜品） ✅
2. 跳转到编辑其他菜品页面 ✅
3. 返回菜单编辑页面 ✅
4. **结果**: 所有被删除的菜品都不会重新出现 ✅

## 兼容性说明

1. **向后兼容**: 修改完全向后兼容，不影响现有功能
2. **数据结构**: 不改变任何数据结构，只是增加过滤逻辑
3. **API接口**: 不影响任何API调用和数据传输
4. **用户体验**: 提升用户体验，解决了数据不一致的问题

## 总结

此次修复通过在所有菜品数据处理环节增加删除状态检查，彻底解决了删除菜品后数据刷新时重新出现的问题。修复方案保持了代码的一致性和健壮性，确保了招牌菜和普通菜品数据的同步，提升了用户体验。
