# MenuEditor 分组删除合并逻辑修复

## 问题描述

删除菜品组后，在数据合并过程中，被删除的分组和分组下的菜品仍然会被合并回来，导致用户删除操作失效。

## 根本原因分析

### 1. 数据合并逻辑缺陷
**问题**: 在`mergeMenuData`函数的第一步中，只检查分组是否在服务端存在，没有检查分组是否被用户删除。

**位置**: 第338-374行
```javascript
// 修复前
oldData.forEach(oldGroup => {
  const newGroup = newGroupMap.get(oldGroup.groupId)
  if (newGroup) { // 只检查服务端是否存在，没有检查删除状态
    // 合并数据...
  }
})

// 修复后
oldData.forEach(oldGroup => {
  const newGroup = newGroupMap.get(oldGroup.groupId)
  if (newGroup && !deletedGroupIds.value.has(oldGroup.groupId)) { // 增加删除状态检查
    // 合并数据...
  }
})
```

### 2. 分组删除时菜品处理不完整
**问题**: 删除分组时，只记录了分组ID，没有记录分组内所有菜品的ID，导致这些菜品可能在其他分组中重新出现。

**位置**: 第745-766行
```javascript
// 修复前
deletedGroup.dishList.forEach(dish => {
  if (dish && dish.specialty === 1 && dish.dishId) {
    // 只处理招牌菜，普通菜品没有记录删除状态
  }
})

// 修复后
deletedGroup.dishList.forEach(dish => {
  if (dish && dish.dishId) {
    // 记录所有菜品的删除状态
    store.dispatch('menuManage/addDeletedDishId', dish.dishId)
    
    // 如果是招牌菜，额外处理
    if (dish.specialty === 1) {
      // 从招牌菜数据中移除
    }
  }
})
```

## 修复方案

### 1. 完善数据合并逻辑
在遍历本地现有数据时，增加分组删除状态检查：
```javascript
if (newGroup && !deletedGroupIds.value.has(oldGroup.groupId)) {
  // 只有分组未被删除时才进行合并
}
```

### 2. 完善分组删除处理
删除分组时，记录分组内所有菜品的删除状态：
```javascript
// 记录分组内所有菜品为已删除
deletedGroup.dishList.forEach(dish => {
  if (dish && dish.dishId) {
    store.dispatch('menuManage/addDeletedDishId', dish.dishId)
  }
})

// 记录分组为已删除
store.dispatch('menuManage/addDeletedGroupId', deletedGroup.groupId)
```

## 修复效果

### ✅ 分组删除完全生效
1. **分组级别过滤**: 被删除的分组不会在数据合并时重新出现
2. **菜品级别过滤**: 被删除分组内的所有菜品也不会在任何地方重新出现
3. **双重保护**: 即使分组和菜品通过不同路径返回，都会被正确过滤

### ✅ 数据一致性保证
1. **删除状态持久化**: 分组和菜品的删除状态在页面跳转后完全保持
2. **招牌菜同步**: 被删除分组中的招牌菜也会从招牌菜数据中正确移除
3. **合并逻辑完整**: 保持原有合并逻辑的所有功能，只是增加删除过滤

## 测试验证

### 测试场景1：基础分组删除
```
1. 创建一个包含多个菜品的分组
2. 删除该分组
3. 跳转到编辑菜品页面
4. 返回菜单编辑页面
✅ 验证：被删除的分组和其内所有菜品都不重新出现
```

### 测试场景2：招牌菜分组删除
```
1. 创建一个包含招牌菜的分组
2. 删除该分组
3. 跳转到编辑菜品页面
4. 返回菜单编辑页面
✅ 验证：被删除的分组不重新出现
✅ 验证：被删除的招牌菜不在招牌菜分组中重新出现
✅ 验证：被删除的招牌菜不在任何普通分组中重新出现
```

### 测试场景3：跨分组菜品删除
```
1. 创建两个分组A和B，都包含相同的菜品X
2. 删除分组A
3. 跳转到编辑菜品页面
4. 返回菜单编辑页面
✅ 验证：分组A不重新出现
✅ 验证：菜品X不在分组B中重新出现（因为菜品X已被标记为删除）
```

### 测试场景4：混合删除操作
```
1. 删除部分分组和部分单独菜品
2. 跳转到编辑菜品页面
3. 返回菜单编辑页面
✅ 验证：所有被删除的分组都不重新出现
✅ 验证：所有被删除的菜品都不重新出现
✅ 验证：未删除的数据正常显示和排序
```

## 技术细节

### 1. 删除状态管理
- **分组删除记录**: `deletedGroupIds` Set存储已删除分组ID
- **菜品删除记录**: `deletedDishIds` Set存储已删除菜品ID
- **双重检查**: 数据合并时同时检查分组和菜品删除状态

### 2. 数据合并策略
```
服务端数据 → 构建映射表 → 遍历本地数据(检查删除状态) → 合并有效数据 → 添加新增数据(检查删除状态)
```

### 3. 删除传播机制
```
删除分组 → 记录分组ID → 记录分组内所有菜品ID → 更新招牌菜数据 → 触发界面更新
```

## 边界情况处理

### 1. 临时分组删除
- 临时分组删除不记录到删除状态（因为它们本来就不存在于服务端）
- 只影响本地界面，不影响数据合并逻辑

### 2. 最后一个分组
- 保持原有逻辑：不允许删除最后一个分组
- 确保界面始终有至少一个分组

### 3. 招牌菜分组
- 招牌菜分组不能被删除（虽然现在是计算属性，但保留检查）
- 招牌菜分组的菜品删除通过单个菜品删除处理

## 总结

通过这次修复，我们彻底解决了分组删除后重新出现的问题：

1. **完善了数据合并逻辑** - 在合并时正确检查分组删除状态
2. **完善了删除处理逻辑** - 删除分组时记录所有相关数据的删除状态
3. **建立了双重保护机制** - 分组级别和菜品级别的删除过滤
4. **保持了数据一致性** - 删除状态在所有数据结构中保持同步

现在用户删除分组后，无论如何操作，被删除的分组和其内的所有菜品都不会重新出现，完全符合用户预期。
