<template>
  <Alert
    :show="show"
    :mask="true"
    @mask="onCancel"
  >
    <template #default>
      <div class="rename-content">
        <div class="rename-title">编辑菜品分组名</div>
        <div class="input-wrapper">
          <TextField
            v-model="inputValue"
            :placeholder="placeholder"
            :maxlength="maxLength"
            class="rename-input"
            :style="{
              padding: '0',
            }"
            :input-style="{
              backgroundColor: 'rgba(48, 48, 52, 0.05)'
            }"
            @change="handleInputChange"
          />
        </div>
        <div v-if="errorMessage" class="error-message">
          {{ errorMessage }}
        </div>
      </div>
    </template>
    <template #footer>
      <div class="alert-footer">
        <Button
          block
          size="medium"
          type="primary"
          :style="{ width: '100%' }"
          :disabled="isConfirmDisabled"
          :loading="isSubmitting"
          @click="onConfirm"
        >
          确认
        </Button>
      </div>
    </template>
  </Alert>
</template>

<script lang="ts" setup>
  import {
    ref,
    nextTick,
    computed,
    watch
  } from 'vue'
  import {
    Alert,
    TextField,
    Button,
    showToast
  } from '@xhs/reds-h5-next'

  interface Props {
    placeholder?: string
    maxLength?: number
    onAudit?: (name: string) => Promise<boolean> // 审核函数
  }

  interface Emits {
    (event: 'confirm', value: string, groupId: string): void
    (event: 'cancel'): void
  }

  const props = withDefaults(defineProps<Props>(), {
    placeholder: '请输入分组名称',
    maxLength: 15,
    onAudit: undefined
  })

  const emit = defineEmits<Emits>()

  const show = ref(false)
  const inputRef = ref<any>()
  const inputValue = ref('')
  const errorMessage = ref('')
  const isSubmitting = ref(false)
  const defaultValue = ref('')
  const existingGroupNames = ref<string[]>([])
  const groupIdValue = ref('')

  // 计算按钮是否应该禁用
  const isConfirmDisabled = computed(() => {
    // 正在提交时禁用
    if (isSubmitting.value) {
      return true
    }

    const trimmedValue = inputValue.value.trim()

    // 检查是否为空
    if (!trimmedValue) {
      return true
    }

    // 检查长度
    if (trimmedValue.length > props.maxLength) {
      return true
    }

    // 检查是否设置为"招牌菜"
    if (trimmedValue.includes('招牌菜')) {
      return true
    }

    // 检查是否重复
    if (existingGroupNames.value.includes(trimmedValue)) {
      return true
    }

    return false
  })

  // 打开弹框方法
  const open = ({ groupName, allGroupNames, groupId }: { groupName: string; allGroupNames: string[]; groupId: string }) => {
    defaultValue.value = groupName
    inputValue.value = groupName
    existingGroupNames.value = allGroupNames
    groupIdValue.value = groupId
    errorMessage.value = ''
    show.value = true

    // 延迟聚焦输入框
    nextTick(() => {
      inputRef.value?.focus()
      inputRef.value?.select()
    })
  }

  // 关闭弹框方法
  const close = () => {
    show.value = false
    defaultValue.value = ''
    inputValue.value = ''
    existingGroupNames.value = []
    errorMessage.value = ''
    isSubmitting.value = false
  }

  // 处理输入长度限制
  const handleInputChange = (value: string) => {
    // 清除错误信息
    if (errorMessage.value) {
      errorMessage.value = ''
    }
    console.log('value', value)
    if (value.length > props.maxLength) {
      console.log('value.length > props.maxLength')
      // 截断到最大长度
      showToast({
        message: `最多${props.maxLength}个字`,
        zIndex: 10000
      })
    }
  }

  // 验证分组名称
  const validateGroupName = (value: string): string | null => {
    const trimmedValue = value.trim()

    // 检查是否为空
    if (!trimmedValue) {
      return '分组名称不能为空'
    }

    // 检查长度
    if (trimmedValue.length > props.maxLength) {
      return `分组名称不能超过${props.maxLength}个字符`
    }

    // 检查是否与原值相同
    if (trimmedValue === defaultValue.value) {
      return null // 没有变化，直接取消
    }

    // 检查是否设置为"招牌菜"
    if (trimmedValue === '招牌菜') {
      return '菜品组名称不符合规范，请修改'
    }

    // 检查是否重复
    if (existingGroupNames.value.includes(trimmedValue)) {
      return '分组名重复'
    }

    return null
  }

  // 确认操作
  const onConfirm = async () => {
    const trimmedValue = inputValue.value.trim()
    // 基础验证
    const validationError = validateGroupName(trimmedValue)
    if (validationError) {
      if (validationError === null && trimmedValue === defaultValue.value) {
        // 没有变化，直接取消
        onCancel()
        return
      }
      errorMessage.value = validationError
      return
    }

    // 如果没有变化，直接取消
    if (trimmedValue === defaultValue.value) {
      onCancel()
      return
    }

    try {
      isSubmitting.value = true
      // 审核通过，提交
      emit('confirm', trimmedValue, groupIdValue.value)
      close()
    } catch (error) {
      console.error('审核失败:', error)
      showToast('菜品组名称不符合规范，请修改')
    } finally {
      isSubmitting.value = false
    }
  }

  // 取消操作
  const onCancel = () => {
    console.log('onCancel')
    emit('cancel')
    close()
  }

  watch(inputValue, newValue => {
    if (newValue.length > props.maxLength) {
      showToast({
        message: `最多${props.maxLength}个字`,
        zIndex: 1000
      })
    }
  })

  // 暴露方法给父组件
  defineExpose({
    open,
    close
  })
</script>

<style lang="stylus" scoped>
.rename-title
  color rgba(0, 0, 0, 0.8)
  text-align center
  font-size 16px
  font-style normal
  font-weight 500
  line-height 24px
  margin-bottom 8px

.input-wrapper
  position relative
  height 48px
  padding 12px 16px
  background rgba(48, 48, 52, 0.05)
  border-radius 12px

.error-message
  color #ff4757
  font-size 12px
  line-height 16px
  margin-top 4px

.special-group-tip
  color rgba(0, 0, 0, 0.45)
  font-size 12px
  line-height 16px
  margin-top 4px
  text-align center

.alert-footer
  width 100%
</style>
