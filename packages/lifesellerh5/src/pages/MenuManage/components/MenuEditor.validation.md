# MenuEditor 修复验证指南

## 修复验证要点

### 🎯 **核心修复内容**
1. **招牌菜数据收集时机调整**: 从数据合并前移动到数据合并后
2. **全面删除状态过滤**: 在所有菜品处理环节增加删除状态检查
3. **数据一致性保证**: 确保招牌菜和普通分组数据同步

### 📋 **验证步骤**

#### 测试场景1：普通菜品删除持久化
```
1. 在菜单编辑页面删除一个普通菜品
2. 跳转到编辑其他菜品页面
3. 返回菜单编辑页面（触发数据刷新和合并）
4. ✅ 验证：被删除的菜品不应该重新出现
```

#### 测试场景2：招牌菜删除持久化
```
1. 在菜单编辑页面删除一个招牌菜
2. 跳转到编辑其他菜品页面
3. 返回菜单编辑页面（触发数据刷新和合并）
4. ✅ 验证：被删除的招牌菜不应该在招牌菜分组中重新出现
5. ✅ 验证：被删除的招牌菜不应该在普通分组中重新出现
```

#### 测试场景3：混合删除持久化
```
1. 删除多个菜品（包括招牌菜和普通菜品）
2. 跳转到编辑其他菜品页面
3. 返回菜单编辑页面
4. ✅ 验证：所有被删除的菜品都不应该重新出现
5. ✅ 验证：招牌菜分组和普通分组数据保持一致
```

#### 测试场景4：数据合并正确性
```
1. 删除部分菜品
2. 在服务端添加新菜品（模拟其他用户操作）
3. 返回菜单编辑页面触发数据合并
4. ✅ 验证：新菜品正确显示
5. ✅ 验证：被删除的菜品仍然不显示
6. ✅ 验证：现有菜品保持原有排序
```

### 🔍 **关键验证点**

#### 1. 招牌菜数据收集时机
- **验证方法**: 在`fetchMenuData`函数中添加断点
- **检查点**: 确认招牌菜收集在`menuGroups.value = mergeMenuData(...)`之后执行
- **预期结果**: 招牌菜数据反映合并后的状态，不包含已删除菜品

#### 2. 删除状态过滤
- **验证方法**: 检查`deletedDishIds.value.has(dish.dishId)`调用
- **检查点**: 所有菜品处理函数都应用删除状态检查
- **预期结果**: 已删除菜品在所有环节都被正确过滤

#### 3. 数据一致性
- **验证方法**: 比较`specialtyDishes.value`和`menuGroups.value`中的招牌菜
- **检查点**: 两个数据源中的招牌菜应该完全一致
- **预期结果**: 删除状态在两个数据结构中保持同步

### 🛠️ **调试工具**

#### 1. 控制台检查
```javascript
// 在浏览器控制台中执行
console.log('删除记录:', store.getters['menuManage/deletedDishIds'])
console.log('招牌菜数据:', specialtyDishes.value)
console.log('普通分组数据:', menuGroups.value)
```

#### 2. Vue DevTools
- 检查`deletedDishIds`计算属性
- 监控`specialtyDishes`和`menuGroups`的变化
- 验证数据合并前后的状态

#### 3. 网络请求监控
- 监控`postQueryMenu`接口返回的原始数据
- 对比合并前后的数据变化
- 确认删除过滤逻辑正确执行

### ⚠️ **注意事项**

1. **测试环境**: 确保在真实的菜单数据环境中测试
2. **数据状态**: 测试前清理浏览器缓存和store状态
3. **并发操作**: 测试多次快速删除和页面跳转的场景
4. **边界情况**: 测试删除所有招牌菜、删除整个分组等极端情况

### 📊 **预期结果**

修复完成后，应该实现：
- ✅ 删除状态在页面跳转后完全持久化
- ✅ 招牌菜和普通分组数据完全同步
- ✅ 数据合并逻辑保持原有功能（排序、新增、更新）
- ✅ 性能无明显影响
- ✅ 用户体验显著改善

### 🚀 **回归测试**

确保修复不影响现有功能：
- ✅ 菜品添加功能正常
- ✅ 菜品编辑功能正常
- ✅ 分组管理功能正常
- ✅ 招牌菜设置功能正常
- ✅ 菜品排序功能正常
- ✅ 数据保存功能正常
