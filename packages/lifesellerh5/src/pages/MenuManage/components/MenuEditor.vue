<template>
  <Loading v-if="loading" />
  <div>
    <NavBar
      title="菜单管理"
      :container-style="containerStyle"
    />

    <div class="menu-content">
      <!-- B端菜单编辑 -->
      <div>
        <!-- 招牌菜分组 - 固定在顶部，不可拖拽 -->
        <div v-if="specialtyGroup && specialtyGroup.dishList.length > 0">
          <MenuGroup
            :group="specialtyGroup"
            :index="0"
            :total-groups="totalGroups"
            :specialty-dish-count="specialtyDishCount"
            :is-specialty="true"
            @update-group="onUpdateGroup"
            @delete-group="onDeleteGroup"
            @add-dish="onAddDish"
            @edit-dish="onEditDish"
            @delete-dish="onDeleteDish"
            @sort-dishes="onSortDishes"
            @rename-group="onRenameGroup"
            @cross-group-drag="onCrossGroupDrag"
          />
        </div>

        <!-- 普通分组 - 可拖拽排序 -->
        <div>
          <draggable
            v-model="regularGroups"
            group="groups"
            item-key="groupId"
            handle=".group-drag-handle"
            @change="onRegularGroupsChange"
          >
            <template #item="{ element: group, index }">
              <div class="group-item">
                <!-- 普通分组的拖拽按钮 -->
                <OnixIcon
                  v-if="regularGroups.length > 1"
                  icon="menu_m"
                  size="24"
                  class="group-drag-handle"
                />
                <MenuGroup
                  :group="group"
                  :index="index + 1"
                  :total-groups="totalGroups"
                  :specialty-dish-count="specialtyDishCount"
                  :is-specialty="false"
                  @update-group="onUpdateGroup"
                  @delete-group="onDeleteGroup"
                  @add-dish="onAddDish"
                  @edit-dish="onEditDish"
                  @delete-dish="onDeleteDish"
                  @sort-dishes="onSortDishes"
                  @rename-group="onRenameGroup"
                  @cross-group-drag="onCrossGroupDrag"
                />
              </div>
            </template>
          </draggable>
        </div>

        <!-- 添加分组按钮 -->
        <div class="add-group-btn" :style="{ marginLeft: regularGroups.length > 1 ? '32px' : 0 }" @click="onAddGroup">
          <span>增加分组</span>
        </div>
      </div>
    </div>

    <!-- 底部按钮区域 -->
    <footer class="bottom-actions">
      <div v-if="hasAnyDish" class="preview-btn" @click="onPreview">
        <OnixIcon icon="eyeView" size="20" />
        <div class="preview-btn-text">预览</div>
      </div>
      <Button
        :disabled="!canSubmit"
        type="primary"
        block
        round
        :loading="saving"
        @click="onSave"
      >
        保存
      </Button>
    </footer>

    <!-- 重命名弹窗 -->
    <RenameAlert
      ref="renameAlertRef"
      @confirm="onRenameConfirm"
      @cancel="onRenameCancel"
    />
    <AlertMethod ref="alertMethodRef" />
  </div>
</template>

<script lang="ts" setup>
  import {
    ref, computed, onMounted, onActivated, onBeforeUnmount, watch, nextTick
  } from 'vue'
  import { useRouter } from 'vue-router'
  import { useStore } from 'vuex'
  import {
    Button,
    showToast,
    ToastType,
  } from '@xhs/reds-h5-next'
  import OnixIcon from '@xhs/onix-icon'
  import { invoke } from '@xhs/ozone-schema'
  import draggable from 'vuedraggable'
  import NavBar from '~/components-next/NavBar.vue'
  import MenuGroup from './MenuGroup.vue'
  import RenameAlert from './RenameAlert.vue'
  import AlertMethod from './AlertMethod.vue'
  import {
    IMenuGroupListExtended, IDishList
  } from '~/types/menu'
  import ROUTE_NAME from '~/constants/routename'
  import Loading from '~/components/loading-next/index.vue'

  import { postQueryMenu } from '~/services/edith_post_query_menu'
  import { postUpsertMenuGroup } from '~/services/edith_post_upsert_menu_group'
  import { postCompleteEdit, IPostCompleteEditPayload } from '~/services/edith_post_complete_edit'

  import '~/assets/svg/menu_m.svg'
  import '~/assets/svg/eyeView.svg'

  interface Props {
    poiId?: string
  }

  const props = withDefaults(defineProps<Props>(), {
    poiId: ''
  })

  const router = useRouter()
  const store = useStore()
  const saving = ref(false)
  const menuGroups = ref<IMenuGroupListExtended[]>([])
  const specialtyDishes = ref<IDishList[]>([]) // 独立的招牌菜数据
  const renameAlertRef = ref()
  const currentRenameGroupId = ref('')
  const alertMethodRef = ref()
  const loading = ref(false)
  const containerStyle = {
    backgroundColor: '#F5F5F5'
  }
  // 页面激活状态管理
  const isFirstLoad = ref(true)
  const previousMenuGroups = ref<IMenuGroupListExtended[]>([])
  const isDataMerging = ref(false)

  // 从store获取删除记录
  const deletedGroupIds = computed(() => store.getters['menuManage/deletedGroupIds'])
  const deletedDishIds = computed(() => store.getters['menuManage/deletedDishIds'])

  // 招牌菜分组（基于独立的招牌菜数据）
  const specialtyGroup = computed(() => {
    // 防御性检查：确保 specialtyDishes 存在且为数组
    if (!Array.isArray(specialtyDishes.value) || specialtyDishes.value.length === 0) {
      return null
    }

    // 过滤掉无效的菜品数据和已删除的菜品
    const validDishes = specialtyDishes.value.filter(dish =>
      dish
      && typeof dish === 'object'
      && dish.dishId
      && dish.dishName
      && !deletedDishIds.value.has(dish.dishId))

    if (validDishes.length === 0) return null

    return {
      groupId: 'specialty',
      groupName: '招牌菜',
      dishList: validDishes,
      sortOrder: 0,
      isSpecialty: true
    }
  })

  // 普通分组（现在就是 menuGroups 本身）
  const regularGroups = computed({
    get: () => menuGroups.value,
    set: (value: IMenuGroupListExtended[]) => {
      menuGroups.value = value
    }
  })

  // 招牌菜数量
  const specialtyDishCount = computed(() => {
    if (!Array.isArray(specialtyDishes.value)) {
      return 0
    }
    return specialtyDishes.value.length
  })

  // 是否可以提交
  // 有菜品，并且名字不等于默认名称（排除临时菜品组）
  const canSubmit = computed(() => {
    // 防御性检查：确保 menuGroups 存在且为数组
    if (!Array.isArray(menuGroups.value) || menuGroups.value.length === 0) {
      return false
    }

    // 过滤掉临时菜品组，只检查已创建的菜品组
    const validGroups = menuGroups.value.filter(group =>
      group && !group.isTemporary && group.groupId && !group.groupId.startsWith('temp_'))

    if (validGroups.length === 0) {
      return false
    }

    // 检查是否有有效的分组和菜品
    return validGroups.some(group =>
      group
      && typeof group === 'object'
      && group.groupName
      && group.groupName !== '菜品分组名称'
      && Array.isArray(group.dishList)
      && group.dishList.length > 0
      && group.dishList.some(dish =>
        dish
        && typeof dish === 'object'
        && dish.dishId
        && dish.dishName))
  })

  // 总分组数量（用于模板）
  const totalGroups = computed(() => menuGroups.value.length)

  // 是否有任意菜品（用于预览按钮显示）
  const hasAnyDish = computed(() => {
    // 检查普通分组中是否有菜品
    if (Array.isArray(menuGroups.value)) {
      return menuGroups.value.some(group =>
        group
        && Array.isArray(group.dishList)
        && group.dishList.length > 0)
    }

    return false
  })

  // 重新计算招牌菜的recommendSortOrder，确保排序连续性
  const recalculateSpecialtyOrder = () => {
    // 防御性检查：确保 specialtyDishes 存在且为数组
    if (!Array.isArray(specialtyDishes.value) || specialtyDishes.value.length === 0) {
      return
    }

    try {
      // 过滤掉已删除的招牌菜，然后重新设置recommendSortOrder
      const validSpecialtyDishes = specialtyDishes.value.filter(dish =>
        dish
        && typeof dish === 'object'
        && dish.dishId
        && !deletedDishIds.value.has(dish.dishId))

      // 重新设置招牌菜数据中菜品的recommendSortOrder
      validSpecialtyDishes.forEach((dish, index) => {
        if (dish && typeof dish === 'object') {
          dish.recommendSortOrder = index + 1
        }
      })

      // 同步更新普通分组中对应菜品的recommendSortOrder
      validSpecialtyDishes.forEach((specialtyDish, index) => {
        if (!specialtyDish || !specialtyDish.dishId) return

        const recommendOrder = index + 1

        // 防御性检查：确保 menuGroups 存在且为数组
        if (!Array.isArray(menuGroups.value)) return

        menuGroups.value.forEach(group => {
          // 防御性检查：确保分组和菜品列表存在
          if (!group || !Array.isArray(group.dishList)) return

          const dish = group.dishList.find(d => d && d.dishId === specialtyDish.dishId)
          if (dish && !deletedDishIds.value.has(dish.dishId)) {
            dish.recommendSortOrder = recommendOrder
          }
        })
      })

      // 更新specialtyDishes数组，移除已删除的菜品
      specialtyDishes.value = validSpecialtyDishes
    } catch (error) {
      console.error('重新计算招牌菜排序失败:', error)
      showToast('更新招牌菜排序失败')
    }
  }

  // 深拷贝函数
  const deepClone = (obj: any): any => {
    if (obj === null || typeof obj !== 'object') return obj
    if (obj instanceof Date) return new Date(obj.getTime())
    if (obj instanceof Array) return obj.map(item => deepClone(item))
    if (typeof obj === 'object') {
      const clonedObj: any = {}
      for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
          clonedObj[key] = deepClone(obj[key])
        }
      }
      return clonedObj
    }
    return obj
  }

  // 智能数据合并逻辑
  // 核心策略：以本地顺序为准，以服务端数据内容为准
  const mergeMenuData = (newData: IMenuGroupListExtended[], oldData: IMenuGroupListExtended[]): IMenuGroupListExtended[] => {
    if (!oldData.length) return newData

    const result: IMenuGroupListExtended[] = []
    const newGroupMap = new Map<string, IMenuGroupListExtended>()
    const newDishMap = new Map<string, IDishList>()
    const processedGroupIds = new Set<string>()

    // 构建服务端数据的映射表
    newData.forEach(group => {
      newGroupMap.set(group.groupId, group)
      group.dishList?.forEach(dish => {
        newDishMap.set(dish.dishId, dish)
      })
    })

    // 第一步：遍历本地现有数据，保持本地顺序
    oldData.forEach(oldGroup => {
      const newGroup = newGroupMap.get(oldGroup.groupId)
      if (newGroup) {
        // 分组在服务端仍存在，合并数据
        const mergedDishes: IDishList[] = []
        const processedDishIds = new Set<string>()

        // 遍历本地菜品，保持本地菜品顺序
        oldGroup.dishList?.forEach(oldDish => {
          const newDish = newDishMap.get(oldDish.dishId)
          if (newDish && !deletedDishIds.value.has(oldDish.dishId)) {
            // 菜品在服务端仍存在且未被用户删除，使用服务端数据内容，保持本地位置
            mergedDishes.push(newDish)
            processedDishIds.add(oldDish.dishId)
          }
          // 如果菜品在服务端不存在或被用户删除，则从本地删除（不添加到mergedDishes）
        })

        // 添加服务端新增的菜品到分组末尾（排除用户已删除的菜品）
        newGroup.dishList?.forEach(newDish => {
          if (!processedDishIds.has(newDish.dishId) && !deletedDishIds.value.has(newDish.dishId)) {
            mergedDishes.push(newDish)
          }
        })

        // 使用服务端分组数据内容，保持本地分组位置
        result.push({
          ...newGroup, // 使用服务端的分组属性（groupName、groupStatus等）
          dishList: mergedDishes
        })

        processedGroupIds.add(oldGroup.groupId)
      }
      // 如果分组在服务端不存在，则从本地删除（不添加到result）
    })

    // 第二步：添加服务端新增的分组到末尾（排除用户已删除的分组）
    newData.forEach(newGroup => {
      if (!processedGroupIds.has(newGroup.groupId) && !deletedGroupIds.value.has(newGroup.groupId)) {
        result.push(newGroup)
      }
    })

    return result
  }

  // 获取菜单数据
  const fetchMenuData = async (shouldMerge = false) => {
    // 防御性检查：确保 poiId 存在
    if (!props.poiId) {
      showToast('店铺ID不能为空')
      return
    }

    try {
      loading.value = true
      isDataMerging.value = shouldMerge

      // 真实接口调用
      const data = await postQueryMenu({ poiId: props.poiId })

      // 防御性检查：确保返回数据结构正确
      if (!data || typeof data !== 'object') {
        throw new Error('接口返回数据格式错误')
      }

      // 防御性检查：确保 menuGroupList 存在且为数组
      if (Array.isArray(data.menuGroupList) && data.menuGroupList.length > 0) {
        // 设置普通分组数据（不包含招牌菜分组）
        const newMenuGroups = data.menuGroupList
          .filter(group => group && typeof group === 'object' && group.groupId) // 过滤无效分组
          .map(group => ({
            ...group,
            isSpecialty: false,
            dishList: Array.isArray(group.dishList) ? group.dishList : [] // 确保 dishList 为数组
          }))

        // 如果需要合并数据且不是首次加载
        if (shouldMerge && !isFirstLoad.value && Array.isArray(previousMenuGroups.value) && previousMenuGroups.value.length > 0) {
          menuGroups.value = mergeMenuData(newMenuGroups, previousMenuGroups.value)
        } else {
          menuGroups.value = newMenuGroups
        }

        // 在数据合并之后收集招牌菜数据，这样才能反映合并后的正确状态
        const newSpecialtyDishes: IDishList[] = []

        // 从合并后的menuGroups中收集招牌菜
        if (Array.isArray(menuGroups.value) && menuGroups.value.length > 0) {
          menuGroups.value.forEach(group => {
            // 防御性检查：确保分组数据有效
            if (!group || typeof group !== 'object') return

            // 防御性检查：确保菜品列表存在且为数组，排除默认分组名称
            if (Array.isArray(group.dishList)
              && group.dishList.length > 0
              && group.groupName
              && group.groupName !== '菜品分组名称') {
              group.dishList.forEach(dish => {
                // 防御性检查：确保菜品数据有效且为招牌菜，并且未被用户删除
                if (dish
                  && typeof dish === 'object'
                  && dish.dishId
                  && dish.dishName
                  && dish.specialty === 1
                  && !deletedDishIds.value.has(dish.dishId)) {
                  newSpecialtyDishes.push(dish)
                }
              })
            }
          })
        }

        // 根据 recommendSortOrder 排序招牌菜
        newSpecialtyDishes.sort((a, b) => {
          const orderA = (a && typeof a.recommendSortOrder === 'number') ? a.recommendSortOrder : 0
          const orderB = (b && typeof b.recommendSortOrder === 'number') ? b.recommendSortOrder : 0
          return orderA - orderB
        })

        // 设置招牌菜数据
        specialtyDishes.value = newSpecialtyDishes

        // 保存当前数据作为下次合并的基础
        previousMenuGroups.value = deepClone(menuGroups.value)

        // 清理删除记录：如果服务端不再有某个分组/菜品，说明它已经被真正删除，可以从删除记录中移除
        if (shouldMerge) {
          // 清理分组删除记录
          if (deletedGroupIds.value.size > 0) {
            const serverGroupIds = new Set(newMenuGroups.map(g => g.groupId))
            const groupsToRemove: string[] = []

            deletedGroupIds.value.forEach((deletedId: string) => {
              if (!serverGroupIds.has(deletedId)) {
                groupsToRemove.push(deletedId)
              }
            })

            groupsToRemove.forEach(id => store.dispatch('menuManage/removeDeletedGroupId', id))
          }

          // 清理菜品删除记录
          if (deletedDishIds.value.size > 0) {
            const serverDishIds = new Set<string>()
            newMenuGroups.forEach(group => {
              group.dishList?.forEach(dish => {
                if (dish && dish.dishId) {
                  serverDishIds.add(dish.dishId)
                }
              })
            })

            const dishesToRemove: string[] = []
            deletedDishIds.value.forEach((deletedId: string) => {
              if (!serverDishIds.has(deletedId)) {
                dishesToRemove.push(deletedId)
              }
            })

            dishesToRemove.forEach(id => store.dispatch('menuManage/removeDeletedDishId', id))
          }
        }
      } else if (!shouldMerge) {
        // 如果没有数据且不是合并操作，创建默认分组
        onAddGroup()
      }
    } catch (error) {
      loading.value = false
      console.error('获取菜单数据失败:', error)
      const errorMessage = error instanceof Error ? error.message : '获取菜单数据失败'
      showToast(errorMessage)

      // 错误情况下的兜底处理
      if (!shouldMerge) {
        menuGroups.value = []
        specialtyDishes.value = []
      }
    } finally {
      isDataMerging.value = false
      loading.value = false
    }
  }

  // 进入预览模式 - 跳转到预览页面
  const onPreview = () => {
    // 这里可以跳转到预览页面或者弹出预览弹窗
    window.open(`xhsdiscover://rn/lancer-life/course-list-preview?poiId=${props.poiId}&isEditing=1`, '_blank')
    const payload = JSON.stringify([specialtyGroup.value, ...menuGroups.value])
    console.log('payload', [specialtyGroup.value, ...menuGroups.value])
    invoke('setItem', {
      key: 'rn_course_card_preview' as any,
      value: payload,
    })
  }

  // 普通分组变更
  const onRegularGroupsChange = () => {
    // 更新sortOrder
    regularGroups.value.forEach((group, index) => {
      group.sortOrder = index + 1 // 从1开始，因为招牌菜分组是0
    })
  }

  // 重命名分组
  const onRenameGroup = (groupId: string) => {
    const group = menuGroups.value.find(g => g.groupId === groupId)

    // 获取所有分组名称，过滤掉当前分组
    const allGroupNames = menuGroups.value
      .filter((g: IMenuGroupListExtended) => g.groupId !== groupId)
      .map((g: IMenuGroupListExtended) => g.groupName)

    renameAlertRef.value?.open({
      groupName: group?.groupName,
      allGroupNames,
      groupId,
    })
  }

  // 重命名确认
  const onRenameConfirm = async (name: string, groupId: string) => {
    // 防御性检查：确保参数有效
    if (!name || typeof name !== 'string' || !groupId || typeof groupId !== 'string') {
      console.error('onRenameConfirm: 无效的参数', { name, groupId })
      showToast('重命名失败：参数无效')
      return
    }

    try {
      // 找到要重命名的菜品组
      const group = menuGroups.value.find(g => g && g.groupId === groupId)
      if (!group) {
        console.error('onRenameConfirm: 找不到指定分组', groupId)
        showToast('重命名失败：找不到指定分组')
        return
      }

      // 区分临时菜品组和已创建菜品组
      if (group.isTemporary) {
        // 临时菜品组，只更新前端数据
        group.groupName = name
        showToast('重命名成功')
      } else {
        // 已创建菜品组，调用接口更新
        const payload = {
          groupId,
          groupName: name,
          poiId: props.poiId,
          groupCategory: 0,
        }

        await postUpsertMenuGroup(payload)
        fetchMenuData()
        showToast('重命名成功')
      }
    } catch (error) {
      console.error('重命名失败:', error)
      showToast('重命名失败')
    }
  }

  // 重命名取消
  const onRenameCancel = () => {
    currentRenameGroupId.value = ''
  }

  // 更新分组
  const onUpdateGroup = (groupId: string, updates: Partial<IMenuGroupListExtended>) => {
    // 防御性检查：确保参数有效
    if (!groupId || typeof groupId !== 'string' || !updates || typeof updates !== 'object') {
      console.error('onUpdateGroup: 无效的参数', { groupId, updates })
      return
    }

    try {
      // 如果更新的是招牌菜分组，直接更新招牌菜数据
      if (groupId === 'specialty' && Array.isArray(updates.dishList)) {
        // 过滤无效的菜品数据
        const validDishes = updates.dishList.filter(dish =>
          dish
          && typeof dish === 'object'
          && dish.dishId
          && dish.dishName)

        specialtyDishes.value = validDishes

        // 同步更新普通分组中对应菜品的招牌菜状态和排序
        validDishes.forEach((specialtyDish, index) => {
          if (!specialtyDish.dishId) return

          const recommendOrder = index + 1

          // 防御性检查：确保 menuGroups 存在且为数组
          if (!Array.isArray(menuGroups.value)) return

          // 在所有普通分组中查找对应菜品并更新
          menuGroups.value.forEach(group => {
            if (!group || !Array.isArray(group.dishList)) return

            const dish = group.dishList.find(d => d && d.dishId === specialtyDish.dishId)
            if (dish) {
              dish.specialty = 1
              dish.recommendSortOrder = recommendOrder
            }
          })
        })
        return
      }

      // 普通分组的更新
      if (!Array.isArray(menuGroups.value)) {
        console.error('onUpdateGroup: menuGroups 不是数组')
        return
      }

      const group = menuGroups.value.find(g => g && g.groupId === groupId)
      if (!group) {
        console.error('onUpdateGroup: 找不到指定分组', groupId)
        return
      }

      Object.assign(group, updates)

      // 如果更新涉及菜品列表，需要检查招牌菜状态变化
      if (Array.isArray(updates.dishList)) {
        updates.dishList.forEach(dish => {
          // 防御性检查：确保菜品数据有效且未被删除
          if (!dish || typeof dish !== 'object' || !dish.dishId || deletedDishIds.value.has(dish.dishId)) return

          // 如果菜品的招牌菜状态发生变化
          if (dish.specialty === 1) {
            // 确保招牌菜数据中也有该菜品
            if (!Array.isArray(specialtyDishes.value)) {
              specialtyDishes.value = []
            }

            const existingIndex = specialtyDishes.value.findIndex(d => d && d.dishId === dish.dishId)
            if (existingIndex === -1) {
              // 如果招牌菜数据中没有该菜品，添加进去
              specialtyDishes.value.push({ ...dish })
            } else {
              // 如果已存在，更新信息
              Object.assign(specialtyDishes.value[existingIndex], dish)
            }
          } else if (dish.specialty === 0) {
            // 如果取消招牌菜状态，从招牌菜数据中移除
            if (Array.isArray(specialtyDishes.value)) {
              const index = specialtyDishes.value.findIndex(d => d && d.dishId === dish.dishId)
              if (index > -1) {
                specialtyDishes.value.splice(index, 1)
              }
            }
          }
        })

        // 重新计算招牌菜排序，确保连续性
        recalculateSpecialtyOrder()
      }
    } catch (error) {
      console.error('更新分组失败:', error)
      showToast('更新分组失败')
    }
  }

  // 删除分组
  const onDeleteGroup = (groupId: string) => {
    // 招牌菜分组不能删除（虽然现在不在menuGroups中，但保留检查以防万一）
    if (groupId === 'specialty') {
      showToast('招牌菜分组不能删除')
      return
    }

    // 只有1个普通分组时不能删除
    if (menuGroups.value.length <= 1) {
      showToast('至少需要保留一个分组')
      return
    }

    alertMethodRef.value.showAlert({
      title: '确认删除分组？',
      message: '组内菜品也会一并删除，且无法恢复，请仔细确认后再操作',
      confirmText: '确认删除',
      cancelText: '取消',
      footerLayout: 'horizontal',
      onConfirm: () => {
        try {
          const index = menuGroups.value.findIndex(g => g.groupId === groupId)
          if (index > -1) {
            const deletedGroup = menuGroups.value[index]

            // 删除该分组内的所有招牌菜
            if (deletedGroup && Array.isArray(deletedGroup.dishList)) {
              deletedGroup.dishList.forEach(dish => {
                if (dish && dish.specialty === 1 && dish.dishId) {
                  // 从招牌菜数据中移除
                  const specialtyIndex = specialtyDishes.value.findIndex(d => d && d.dishId === dish.dishId)
                  if (specialtyIndex > -1) {
                    specialtyDishes.value.splice(specialtyIndex, 1)
                  }
                }
              })
            }

            // 记录被删除的分组ID（排除临时分组）
            if (!deletedGroup.isTemporary && deletedGroup.groupId && !deletedGroup.groupId.startsWith('temp_')) {
              store.dispatch('menuManage/addDeletedGroupId', deletedGroup.groupId)
            }

            // 删除分组
            menuGroups.value.splice(index, 1)

            // 重新计算招牌菜排序，确保连续性
            recalculateSpecialtyOrder()

            showToast('分组已删除')
          }
        } catch (error) {
          console.error('删除分组失败:', error)
          showToast('删除分组失败')
        }
      },
      onCancel: () => {}
    })
  }

  // 添加菜品
  const onAddDish = async (groupId: string) => {
    // 防御性检查：确保参数有效
    if (!groupId || typeof groupId !== 'string') {
      console.error('onAddDish: 无效的分组ID', groupId)
      showToast('添加菜品失败：分组ID无效')
      return
    }

    try {
      // 检查是否为临时菜品组
      const group = menuGroups.value.find(g => g && g.groupId === groupId)
      if (!group) {
        console.error('onAddDish: 找不到指定分组', groupId)
        showToast('添加菜品失败：找不到指定分组')
        return
      }

      let finalGroupId = groupId

      // 如果是临时菜品组，先创建正式菜品组
      if (group.isTemporary) {
        const newGroupId = await createMenuGroupFromTemporary(groupId, group.groupName)
        if (!newGroupId) {
          // 创建失败，不继续添加菜品
          return
        }
        finalGroupId = newGroupId
      }

      // 跳转到菜品创建页面
      router.push({
        name: ROUTE_NAME.DISH_CREATE,
        query: {
          groupId: finalGroupId,
          poiId: props.poiId,
        }
      })
    } catch (error) {
      console.error('添加菜品失败:', error)
      showToast('添加菜品失败')
    }
  }

  // 编辑菜品
  const onEditDish = (dish: IDishList) => {
    // 将菜品数据存入 store
    store.dispatch('menuManage/setDish', dish)

    router.push({
      name: ROUTE_NAME.DISH_CREATE,
      query: {
        id: dish.dishId,
        poiId: props.poiId,
        isEdit: 'true',
        fullscreen: 'true',
      }
    })
  }

  // 删除菜品
  const onDeleteDish = (groupId: string, dishId: string) => {
    // 防御性检查：确保参数有效
    if (!groupId || typeof groupId !== 'string' || !dishId || typeof dishId !== 'string') {
      console.error('onDeleteDish: 无效的参数', { groupId, dishId })
      showToast('删除失败：参数无效')
      return
    }

    // 防御性检查：确保 alertMethodRef 存在
    if (!alertMethodRef.value || typeof alertMethodRef.value.showAlert !== 'function') {
      console.error('onDeleteDish: alertMethodRef 不可用')
      showToast('删除失败：系统错误')
      return
    }

    alertMethodRef.value.showAlert({
      title: '确认删除菜品？',
      message: '菜品删除无法恢复，请仔细确认后再操作',
      confirmText: '确认删除',
      cancelText: '取消',
      footerLayout: 'horizontal',
      onConfirm: () => {
        try {
          // 防御性检查：确保 menuGroups 存在且为数组
          if (!Array.isArray(menuGroups.value)) {
            throw new Error('menuGroups 不是数组')
          }

          const group = menuGroups.value.find(g => g && g.groupId === groupId)
          if (!group) {
            throw new Error(`找不到分组: ${groupId}`)
          }

          // 防御性检查：确保分组有菜品列表
          if (!Array.isArray(group.dishList)) {
            throw new Error('分组菜品列表无效')
          }

          const dishIndex = group.dishList.findIndex(d => d && d.dishId === dishId)
          if (dishIndex === -1) {
            throw new Error(`找不到菜品: ${dishId}`)
          }

          const dish = group.dishList[dishIndex]
          if (!dish || typeof dish !== 'object') {
            throw new Error('菜品数据无效')
          }

          // 记录被删除的菜品ID（用于数据合并时过滤）
          store.dispatch('menuManage/addDeletedDishId', dishId)

          // 如果删除的是招牌菜，需要同步处理
          if (dish.specialty === 1) {
            // 从普通分组中删除该菜品
            group.dishList.splice(dishIndex, 1)

            // 从招牌菜数据中删除该菜品
            if (Array.isArray(specialtyDishes.value)) {
              const specialtyIndex = specialtyDishes.value.findIndex(d => d && d.dishId === dishId)
              if (specialtyIndex > -1) {
                specialtyDishes.value.splice(specialtyIndex, 1)
              }
            }

            // 重新计算招牌菜排序，确保连续性
            recalculateSpecialtyOrder()
          } else {
            // 普通菜品只从当前分组删除
            group.dishList.splice(dishIndex, 1)
          }

          showToast({
            type: ToastType.ToastBuiltInType.TEXT,
            message: '菜品已删除'
          })
        } catch (error) {
          console.error('删除菜品失败:', error)
          const errorMessage = error instanceof Error ? error.message : '删除菜品失败'
          showToast(`删除失败：${errorMessage}`)
        }
      },
      onCancel: () => {}
    })
  }

  // 菜品排序
  const onSortDishes = (groupId: string, dishes: IDishList[]) => {
    // 防御性检查：确保参数有效
    if (!groupId || typeof groupId !== 'string') {
      console.error('onSortDishes: groupId 无效', groupId)
      return
    }

    if (!Array.isArray(dishes)) {
      console.error('onSortDishes: dishes 不是数组', dishes)
      return
    }

    try {
      // 如果是招牌菜分组的排序，直接更新招牌菜数据
      if (groupId === 'specialty') {
        // 过滤无效的菜品数据和已删除的菜品
        const validDishes = dishes.filter(dish =>
          dish
          && typeof dish === 'object'
          && dish.dishId
          && dish.dishName
          && !deletedDishIds.value.has(dish.dishId))

        specialtyDishes.value = validDishes
        recalculateSpecialtyOrder()
        return
      }

      // 普通分组的排序
      if (!Array.isArray(menuGroups.value)) {
        console.error('onSortDishes: menuGroups 不是数组')
        return
      }

      const group = menuGroups.value.find(g => g && g.groupId === groupId)
      if (!group) {
        console.error('onSortDishes: 找不到指定分组', groupId)
        return
      }

      // 过滤无效的菜品数据和已删除的菜品
      const validDishes = dishes.filter(dish =>
        dish
        && typeof dish === 'object'
        && dish.dishId
        && dish.dishName
        && !deletedDishIds.value.has(dish.dishId))

      group.dishList = validDishes
    } catch (error) {
      console.error('菜品排序失败:', error)
      showToast('菜品排序失败')
    }
  }

  // 处理跨组拖拽
  const onCrossGroupDrag = (dragInfo: {
    fromGroupId: string
    toGroupId: string
    dishId: string
    oldIndex: number
    newIndex: number
  }) => {
    const {
      fromGroupId, toGroupId, dishId, newIndex
    } = dragInfo

    // 找到源分组和目标分组
    const fromGroup = menuGroups.value.find(g => g.groupId === fromGroupId)
    const toGroup = menuGroups.value.find(g => g.groupId === toGroupId)

    if (!fromGroup || !toGroup) return

    // 不允许拖拽到招牌菜分组
    if (toGroup.isSpecialty) {
      showToast('不能将菜品拖拽到招牌菜分组')
      return
    }

    // 找到要移动的菜品
    const dishIndex = fromGroup.dishList.findIndex(d => d.dishId === dishId)
    if (dishIndex === -1) return

    const dish = fromGroup.dishList[dishIndex]

    // 如果是从招牌菜分组拖出，需要取消specialty标识
    if (fromGroup.isSpecialty) {
      dish.specialty = 0
      dish.recommendSortOrder = undefined
    }

    // 从源分组移除菜品
    fromGroup.dishList.splice(dishIndex, 1)

    // 添加到目标分组
    toGroup.dishList.splice(newIndex, 0, dish)

    // 更新招牌菜分组（如果从招牌菜分组拖出，需要重新收集招牌菜）
    if (fromGroup.isSpecialty) {
      const specialtyDishes: IDishList[] = []
      menuGroups.value.forEach(group => {
        if (!group.isSpecialty) {
          group.dishList.forEach(dish => {
            if (dish.specialty === 1) {
              specialtyDishes.push(dish)
            }
          })
        }
      })

      // 按 recommendSortOrder 排序
      specialtyDishes.sort((a, b) => {
        const orderA = a.recommendSortOrder || 0
        const orderB = b.recommendSortOrder || 0
        return orderA - orderB
      })

      fromGroup.dishList = specialtyDishes
    }

    showToast('菜品已移动')
  }

  // 创建菜品组的辅助函数（将临时菜品组转为正式菜品组）
  const createMenuGroupFromTemporary = async (tempGroupId: string, groupName?: string): Promise<string | null> => {
    // 防御性检查：确保参数有效
    if (!tempGroupId || typeof tempGroupId !== 'string') {
      console.error('createMenuGroupFromTemporary: 无效的临时分组ID', tempGroupId)
      return null
    }

    // 防御性检查：确保 poiId 存在
    if (!props.poiId) {
      console.error('createMenuGroupFromTemporary: poiId 不能为空')
      showToast('创建分组失败：店铺ID不能为空')
      return null
    }

    try {
      // 找到临时菜品组
      const tempGroup = menuGroups.value.find(g => g && g.groupId === tempGroupId)
      if (!tempGroup) {
        console.error('createMenuGroupFromTemporary: 找不到临时分组', tempGroupId)
        return null
      }

      // 使用传入的名称或者临时分组的当前名称
      const finalGroupName = groupName || tempGroup.groupName

      const payload = {
        groupName: finalGroupName,
        poiId: props.poiId,
        groupCategory: 0,
      }

      // 调用接口创建菜品组
      await postUpsertMenuGroup(payload)

      // 重新获取数据以获得真实的groupId
      await fetchMenuData()

      // 查找新创建的菜品组（通过名称匹配）
      const newGroup = menuGroups.value.find(g =>
        g && g.groupName === finalGroupName && !g.isTemporary)

      if (newGroup && newGroup.groupId) {
        return newGroup.groupId
      }
      console.error('createMenuGroupFromTemporary: 创建后找不到新分组')
      return null
    } catch (error) {
      console.error('创建菜品组失败:', error)
      showToast('创建分组失败')
      return null
    }
  }

  // 添加分组（创建临时菜品组）
  const onAddGroup = () => {
    try {
      // 防御性检查：确保 menuGroups 存在且为数组
      if (!Array.isArray(menuGroups.value)) {
        console.error('onAddGroup: menuGroups 不是数组')
        menuGroups.value = []
      }

      // 生成临时ID
      const tempGroupId = `temp_${Date.now()}`

      // 计算排序值
      const nextSortOrder = menuGroups.value.length + 1

      // 创建临时菜品组对象
      const tempGroup: IMenuGroupListExtended = {
        groupId: tempGroupId,
        groupName: '菜品分组名称',
        sortOrder: nextSortOrder,
        dishList: [],
        isSpecialty: false,
        isTemporary: true
      }

      // 添加到前端数据中
      menuGroups.value.push(tempGroup)

      showToast('添加分组成功')
    } catch (error) {
      console.error('添加临时分组失败:', error)
      showToast('添加分组失败')
    }
  }

  // 保存菜单
  const onSave = async () => {
    // 防御性检查：确保不重复保存
    if (saving.value) {
      console.warn('onSave: 正在保存中，忽略重复请求')
      return
    }

    // 防御性检查：确保 poiId 存在
    if (!props.poiId) {
      showToast('保存失败：店铺ID不能为空')
      return
    }

    saving.value = true
    try {
      // 防御性检查：确保 menuGroups 存在且为数组
      if (!Array.isArray(menuGroups.value)) {
        throw new Error('菜单分组数据无效')
      }

      // 根据招牌菜数据中菜品的顺序设置recommendSortOrder
      // 首先清除所有菜品的recommendSortOrder
      menuGroups.value.forEach(group => {
        if (group && Array.isArray(group.dishList)) {
          group.dishList.forEach(dish => {
            if (dish && typeof dish === 'object') {
              dish.recommendSortOrder = undefined
            }
          })
        }
      })

      // 然后根据招牌菜数据中菜品的顺序重新设置recommendSortOrder
      if (Array.isArray(specialtyDishes.value) && specialtyDishes.value.length > 0) {
        // 遍历招牌菜数据中的菜品，根据数组索引设置recommendSortOrder
        specialtyDishes.value.forEach((specialtyDish, index) => {
          if (!specialtyDish || !specialtyDish.dishId) return

          const recommendOrder = index + 1 // 从1开始

          // 在所有分组中查找对应的菜品并设置recommendSortOrder
          menuGroups.value.forEach(group => {
            if (!group || !Array.isArray(group.dishList)) return

            group.dishList.forEach(dish => {
              if (dish && dish.dishId === specialtyDish.dishId) {
                dish.recommendSortOrder = recommendOrder
              }
            })
          })
        })
      }

      // 过滤掉临时菜品组，只保存已创建的菜品组
      const validMenuGroups = menuGroups.value.filter(group =>
        group && !group.isTemporary && group.groupId && !group.groupId.startsWith('temp_'))

      const payload: IPostCompleteEditPayload = {
        poiId: props.poiId,
        menuGroupCompleteList: validMenuGroups.map(group => ({
          ...group,
          dishCompleteList: group.dishList.map(dish => ({
            ...dish,
            recommendSortOrder: dish.recommendSortOrder
          }))
        }))
      }

      console.log('payload', payload)
      await postCompleteEdit(payload)
      showToast('菜单已保存')
    } catch (error) {
      console.error('保存失败:', error)
      const errorMessage = error instanceof Error ? error.message : '保存失败，请重试'
      showToast(errorMessage)
    } finally {
      saving.value = false
    }
  }

  // 防抖处理的页面激活逻辑
  let activationTimer: any = null

  // 页面激活时的数据刷新逻辑
  const handlePageActivation = async () => {
    if (!isFirstLoad.value && !isDataMerging.value) {
      // 清除之前的定时器
      if (activationTimer) {
        clearTimeout(activationTimer)
      }

      // 防抖处理，避免频繁调用
      activationTimer = setTimeout(async () => {
        try {
          // 非首次加载时，进行智能数据合并
          await fetchMenuData(true)
        } catch (error) {
          console.error('页面激活时获取数据失败:', error)
        }
      }, 300) // 300ms防抖
    }
  }

  // 页面可见性变化处理
  const handleVisibilityChange = async () => {
    if (!document.hidden && !isFirstLoad.value) {
      // 页面从隐藏变为可见，且不是首次加载
      await handlePageActivation()
    }
  }

  // 监听页面激活事件（用于keep-alive组件）
  onActivated(handlePageActivation)

  // 监听页面可见性变化（用于处理浏览器标签页切换、router.back()等情况）
  watch(() => router.currentRoute.value.fullPath, async (newPath, oldPath) => {
    if (newPath && oldPath && newPath !== oldPath) {
      // 路由发生了真实变化，重置首次加载标识
      await nextTick()
      if (!isFirstLoad.value) {
        await handlePageActivation()
      }
    }
  })

  // 窗口焦点变化处理（用于检测router.back()等情况）
  const handleWindowFocus = async () => {
    if (!isFirstLoad.value) {
      await handlePageActivation()
    }
  }

  onMounted(async () => {
    await fetchMenuData()
    isFirstLoad.value = false

    // 添加页面可见性变化监听
    document.addEventListener('visibilitychange', handleVisibilityChange)
    // 添加窗口焦点监听（用于检测页面重新激活）
    window.addEventListener('focus', handleWindowFocus)
  })

  onBeforeUnmount(() => {
    // 清理定时器
    if (activationTimer) {
      clearTimeout(activationTimer)
    }

    // 清理事件监听器
    document.removeEventListener('visibilitychange', handleVisibilityChange)
    window.removeEventListener('focus', handleWindowFocus)
  })
</script>

<style lang="stylus" scoped>
.nav-actions
  .action-btn
    font-size 14px
    color #1890ff
    cursor pointer

.menu-content
  height calc(100vh - 120px)
  overflow-y auto
  padding-bottom 10px
  padding 16px

.bottom-actions
  position fixed
  bottom 0
  left 0
  right 0
  padding 16px
  background white
  border-top 1px solid #f0f0f0
  display flex
  justify-content center
  align-items center
  gap 16px
  @supports (padding-bottom: env(safe-area-inset-bottom, 16px)) {
      padding-bottom: env(safe-area-inset-bottom, 16px);
    }
  @supports (padding-bottom: constant(safe-area-inset-bottom, 16px)) {
    padding-bottom: constant(safe-area-inset-bottom, 16px);
  }

.preview-btn
  display flex
  flex-direction column
  align-items center
  justify-content center
  color rgba(0, 0, 0, 0.62)
  .preview-btn-text
    color rgba(0, 0, 0, 0.62)
    font-size 10px
    line-height 14px
    text-wrap nowrap

.add-group-btn
  display flex
  align-items center
  justify-content center
  height 44px
  background rgba(255, 255, 255, 1)
  border-radius 12px
  color rgba(0, 0, 0, 0.62)
  font-size 16px
  font-weight 500
  line-height 24px

.group-item
  display flex
  align-items flex-start
  gap 8px
  .group-drag-handle
    color rgba(0, 0, 0, 0.45)
    margin-top 10px

.data-merging-tip
  position fixed
  top 0
  left 0
  right 0
  bottom 0
  background rgba(0, 0, 0, 0.3)
  display flex
  align-items center
  justify-content center
  z-index 1000

  .merging-content
    background white
    border-radius 12px
    padding 24px
    display flex
    flex-direction column
    align-items center
    gap 12px
    box-shadow 0 4px 12px rgba(0, 0, 0, 0.15)

    .merging-icon
      font-size 24px
      animation rotate 1s linear infinite

    .merging-text
      font-size 16px
      color rgba(0, 0, 0, 0.85)
      font-weight 500
</style>
